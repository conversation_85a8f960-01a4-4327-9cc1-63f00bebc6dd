# 📊 Random Facts Generation API

> **AI-powered financial facts generator** that creates engaging stock market insights in English and Arabic

## 🎯 What This Project Does

This system automatically generates **interesting facts about stock market data** using:
- **Real financial data** from Google BigQuery
- **AI technology** (Google Gemini) to create human-readable facts
- **Smart caching** to improve performance and reduce costs

### Example Facts Generated:
- *"On 2024-01-15, Apple Inc reached its highest trading value in 52 weeks, with an increase of 3.2% from the previous day"*
- *"Tesla was among the most active stocks with total traded value 2.5 billion SAR in the last 52 weeks"*

## ✨ Key Features

- 🤖 **AI-Powered**: Uses Google Gemini to convert raw data into engaging facts
- 🌍 **Multi-Language**: Supports English, Arabic, and bilingual output
- ⚡ **Smart Caching**: Facts cached for 24 hours to improve speed
- 📈 **8 Fact Types**: Different categories of financial insights
- 🔄 **Auto-Refresh**: New facts generated daily
- 🚀 **REST API**: Easy to integrate with other applications

## 🏗️ Architecture

```
User Request → Flask API → BigQuery Data → AI Processing → Cached Facts → JSON Response
```

### Main Components:
1. **Flask Web API** - <PERSON>les requests and responses
2. **BigQuery Integration** - Fetches real financial data
3. **Google Gemini AI** - Generates human-readable facts
4. **Cloud Storage** - Caches facts for better performance
5. **YAML Configurations** - Defines different fact types

## 📋 Fact Types Available

| Type | Description | Example |
|------|-------------|---------|
| **52-Week Highs** | Companies at peak trading values | "Company X reached highest value in 52 weeks" |
| **Weekly Performance** | Best weekly trading achievements | "Company Y achieved highest weekly value" |
| **Free Float Analysis** | Highest/lowest free float percentages | "Company Z has 85% free float stocks" |
| **Top Gainers** | Best performing stocks | "Company A gained 45% in 52 weeks" |
| **Market Leaders** | Highest market capitalizations | "Company B leads with $2B market cap" |
| **Liquidity Champions** | Most actively traded stocks | "Company C traded $500M in 52 weeks" |
| **52-Week Lows** | Companies at lowest prices | "Company D hit 52-week low price" |
| **P/E Insights** | Top gainers with P/E ratios | "Company E gained 30% with P/E ratio 15.2" |

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Google Cloud account with BigQuery access
- Google API key for Gemini AI

### 1. Environment Setup
Create a `.env` file:
```env
GOOGLE_API_KEY=your_gemini_api_key
FACTS_CACHE_BUCKET=your-storage-bucket
LANGSMITH_API_KEY=your_langsmith_key
LANGSMITH_PROJECT=your-project-name
```

### 2. Run with Docker
```bash
# Build and start the application
docker-compose up --build

# The API will be available at http://localhost:8080
```

### 3. Test the API
```bash
# Get basic info
curl http://localhost:8080/

# Get 5 random facts in English
curl "http://localhost:8080/facts?lang=en&count=5"

# Get bilingual facts
curl "http://localhost:8080/facts?lang=both&count=3"
```

## 📡 API Endpoints

### Main Endpoints
- `GET /` - API information and status
- `GET /facts` - **Main endpoint** to get random facts
- `GET /health` - Check if system is working
- `GET /debug` - Detailed system information

### Parameters for `/facts`
| Parameter | Options | Default | Description |
|-----------|---------|---------|-------------|
| `lang` | `en`, `ar`, `both` | `both` | Language for facts |
| `count` | 1-20 | 8 | Number of facts to return |
| `refresh` | `true`, `false` | `false` | Force new facts generation |

### Example Requests
```bash
# English facts only
GET /facts?lang=en&count=5

# Arabic facts only  
GET /facts?lang=ar&count=3

# Force refresh cache
GET /facts?refresh=true

# Bilingual facts
GET /facts?lang=both&count=10
```

## 🔧 Configuration

### Adding New Fact Types
Create new YAML files in `facts_config/` directory:

```yaml
fact_id: my_new_fact
fact_format: |
  On [Date], [Company X] achieved [Metric] with [Value]
required_tables:
  - your.bigquery.table
sql_query: |
  SELECT company_name, date, metric_value
  FROM your.bigquery.table
  WHERE conditions
  LIMIT 5
```

## 🗂️ Project Structure

```
Random-Facts-Generation/
├── main.py                 # Main Flask application
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose setup
├── facts_config/          # Fact type definitions
│   ├── format_1.yaml     # 52-week highs
│   ├── format_2.yaml     # Weekly performance
│   └── ...               # Other fact types
└── positive-theme-*.json  # Google Cloud credentials
```

## 🔍 How It Works

1. **Load Configuration**: System reads fact definitions from YAML files
2. **Fetch Data**: Complex SQL queries get data from BigQuery
3. **AI Processing**: Gemini AI converts data into readable facts
4. **Cache Results**: Facts stored in Cloud Storage for 24 hours
5. **Serve API**: Random facts returned via REST API

## 🌍 Language Support

- **English**: Standard financial facts
- **Arabic**: Saudi dialect (اللهجة السعودية)
- **Both**: JSON format with both languages

Example bilingual response:
```json
{
  "facts": [
    {
      "statement": {
        "en": "Apple reached its highest trading value",
        "ar": "وصلت شركة آبل إلى أعلى قيمة تداول"
      }
    }
  ]
}
```

## 🛠️ Development

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export GOOGLE_API_KEY=your_key

# Run locally
python main.py
```

### Adding New Features
1. Create new YAML configuration in `facts_config/`
2. Define SQL query and fact format
3. Test with `/facts?refresh=true`

## 📊 Monitoring

- **Health Check**: `GET /health`
- **Debug Info**: `GET /debug`
- **Cache Status**: `GET /cache/status`
- **LangSmith Tracing**: Automatic LLM monitoring

## 🚨 Troubleshooting

### Common Issues
1. **"BigQuery client not initialized"**
   - Check Google Cloud credentials
   - Verify service account permissions

2. **"LLM not initialized"**
   - Verify GOOGLE_API_KEY is set
   - Check Gemini API access

3. **"No facts generated"**
   - Check BigQuery data availability
   - Verify YAML configurations

### Debug Commands
```bash
# Check system status
curl http://localhost:8080/debug

# Clear cache and regenerate
curl -X POST http://localhost:8080/cache/clear
curl "http://localhost:8080/facts?refresh=true"
```

## 📝 License

This project is for educational and commercial use. Make sure to comply with Google Cloud and API usage terms.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add new fact types or improve existing ones
4. Test thoroughly
5. Submit pull request

---

**Made with ❤️ for financial data enthusiasts**
