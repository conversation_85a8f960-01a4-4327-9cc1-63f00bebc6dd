# 📊 Random Facts Generation API

> AI-powered financial facts generator for stock market insights

## 🎯 What This Does

Generates financial facts about stock market data using AI and real data from BigQuery.

**Example**: *"Apple Inc reached its highest trading value in 52 weeks with 3.2% increase"*

## ✨ Features

- 🤖 AI-powered fact generation
- 🌍 English and Arabic support
- ⚡ Smart caching
- 🚀 REST API

## 🚀 Quick Start

1. Set environment variables in `.env` file
2. Run: `docker-compose up --build`
3. API available at: `http://localhost:8080`

## 📡 API Usage

**Main endpoint**: `GET /facts`

**Parameters**:
- `lang`: `en`, `ar`, `both` (default: `both`)
- `count`: 1-20 (default: 8)
- `refresh`: `true`/`false` (default: `false`)

**Example**: `GET /facts?lang=en&count=5`

## 🔧 Configuration

YAML files in `facts_config/` define 8 different fact types. Each file contains:
- Fact template format
- SQL query for data
- Required BigQuery tables

## 🗂️ Files

```
├── main.py              # Flask API
├── requirements.txt     # Dependencies
├── Dockerfile          # Container setup
├── facts_config/       # 8 YAML fact definitions
└── docker-compose.yml  # Docker config
```

## 🛠️ Development

```bash
pip install -r requirements.txt
python main.py
```
